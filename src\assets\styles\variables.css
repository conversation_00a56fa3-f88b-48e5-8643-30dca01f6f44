:root {
    --primary-color: #5B6AFF;
    --secondary-color: #8C54FF;
    --accent-color: #667EEA;
    --bg-primary: #FFFFFF;
    --bg-secondary: #F5F7FA;
    --bg-card: #FFFFFF;
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-muted: #999999;
    --border-radius: 12px;
    --shadow: 0 4px 12px rgba(0,0,0,0.08);
    --spacing-unit: 8px;
}

[data-theme="dark"] {
    --primary-color: #5B6AFF;
    --secondary-color: #8C54FF;
    --accent-color: #667EEA;
    --bg-primary: #1a1a1a;
    --bg-secondary: #2a2a2a;
    --bg-card: #252525;
    --text-primary: #FFFFFF;
    --text-secondary: #CCCCCC;
    --text-muted: #999999;
}

/* 吉卜力主题 - 受工作室艺术风格启发 */
[data-theme="ghibli"] {
    /* 主色调 - 田园绿色和天空蓝 */
    --primary-color: #7FB069; /* 龙猫绿 */
    --secondary-color: #87CEEB; /* 天空蓝 */
    --accent-color: #D4A574; /* 温暖大地色调 */

    /* 强调色 - 来自标志性电影 */
    --totoro-green: #7FB069;
    --spirited-red: #E74C3C;
    --howl-blue: #5DADE2;

    /* 背景色 - 水彩纸质感 */
    --bg-primary: #FDF6E3; /* 温暖的奶油白 */
    --bg-secondary: #F4F1E8; /* 淡雅的纸质色 */
    --bg-card: #FFFFFF;

    /* 文字色彩 - 柔和自然 */
    --text-primary: #2C3E50; /* 深蓝灰 */
    --text-secondary: #5D6D7E; /* 中性灰蓝 */
    --text-muted: #85929E; /* 淡灰蓝 */

    /* 特殊效果 */
    --border-radius: 16px; /* 更圆润的边角 */
    --shadow: 0 8px 24px rgba(123, 176, 105, 0.15); /* 绿色调阴影 */
    --ghibli-gradient: linear-gradient(135deg, #7FB069 0%, #87CEEB 50%, #D4A574 100%);

    /* 动画和过渡 */
    --transition-smooth: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --animation-float: float 6s ease-in-out infinite;
}
