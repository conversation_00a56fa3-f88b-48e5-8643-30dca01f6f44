/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-in-left': 'slideInLeft 0.3s ease-out',
        'slide-out-left': 'slideOutLeft 0.3s ease-in',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'border-glow': 'borderGlow 3s ease-in-out infinite',
        'stroke-dash': 'strokeDash 2s ease-in-out infinite',
        'particle-float': 'particleFloat 4s ease-in-out infinite',
        'gradient-shift': 'gradientShift 3s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideOutLeft: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-100%)' },
        },
        glow: {
          '0%': { 
            boxShadow: '0 0 5px rgba(59, 130, 246, 0.5)',
            filter: 'drop-shadow(0 0 10px rgba(59, 130, 246, 0.3))'
          },
          '100%': { 
            boxShadow: '0 0 20px rgba(59, 130, 246, 0.8)',
            filter: 'drop-shadow(0 0 20px rgba(59, 130, 246, 0.6))'
          },
        },
        borderGlow: {
          '0%, 100%': { 
            strokeDashoffset: '0',
            filter: 'drop-shadow(0 0 5px rgba(59, 130, 246, 0.5))'
          },
          '50%': { 
            strokeDashoffset: '100',
            filter: 'drop-shadow(0 0 15px rgba(59, 130, 246, 0.8))'
          },
        },
        strokeDash: {
          '0%': { strokeDasharray: '0 100' },
          '50%': { strokeDasharray: '50 50' },
          '100%': { strokeDasharray: '100 0' },
        },
        particleFloat: {
          '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
          '33%': { transform: 'translateY(-10px) rotate(120deg)' },
          '66%': { transform: 'translateY(5px) rotate(240deg)' },
        },
        gradientShift: {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
        },
      },
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'animated-gradient': 'linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab)',
      },
      backgroundSize: {
        '400%': '400% 400%',
      },
    },
  },
  plugins: [
    require('tailwindcss-animate'),
  ],
}
