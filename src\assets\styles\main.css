@import "tailwindcss";
@import './variables.css';
@import './ghibli-theme.css';

/* Tailwind 组件层 */
@layer components {
  .tool-card-enhanced {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700 p-6;
  }

  .tool-card-enhanced:hover {
    @apply transform -translate-y-1 scale-[1.02];
  }

  .btn-primary-enhanced {
    @apply bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg w-full;
  }

  .search-input-enhanced {
    @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  }

  .nav-item-enhanced {
    @apply flex items-center px-4 py-3 text-sm font-medium rounded-lg mx-2 my-1 transition-all duration-200 cursor-pointer text-gray-700 dark:text-gray-300;
  }

  .nav-item-enhanced:hover {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  .nav-item-enhanced.active {
    @apply bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200;
  }
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    margin: 0;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
    display: grid;
    grid-template-areas:
        "header header"
        "sidebar main";
    grid-template-columns: 240px 1fr;
    grid-template-rows: 60px 1fr;
    height: 100vh;
}

.header {
    grid-area: header;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 calc(var(--spacing-unit) * 3);
    background-color: var(--bg-primary);
    box-shadow: var(--shadow);
    z-index: 10;
}

.logo h1 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin: 0;
}

.search-bar {
    flex-grow: 1;
    margin: 0 calc(var(--spacing-unit) * 4);
}

.search-bar input {
    width: 100%;
    max-width: 500px;
    padding: calc(var(--spacing-unit) * 1.5);
    border-radius: var(--border-radius);
    border: 1px solid var(--bg-secondary);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 14px;
}

.search-bar input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.theme-toggle {
    padding: var(--spacing-unit) calc(var(--spacing-unit) * 2);
    border: none;
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.theme-toggle:hover {
    background-color: var(--secondary-color);
}

.sidebar {
    grid-area: sidebar;
    background-color: var(--bg-primary);
    padding: calc(var(--spacing-unit) * 2);
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar nav ul li a {
    display: block;
    padding: calc(var(--spacing-unit) * 1.5);
    text-decoration: none;
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.sidebar nav ul li a.active,
.sidebar nav ul li a:hover {
    background-color: var(--bg-secondary);
    color: var(--primary-color);
}

.main-content {
    grid-area: main;
    overflow-y: auto;
    padding: calc(var(--spacing-unit) * 3);
}

.tool-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: calc(var(--spacing-unit) * 3);
}

.tool-card {
    background-color: var(--bg-card);
    border-radius: var(--border-radius);
    padding: calc(var(--spacing-unit) * 3);
    box-shadow: var(--shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.tool-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.card-icon {
    font-size: 2rem;
    margin-bottom: var(--spacing-unit);
}

.card-title {
    font-size: 1.2rem;
    margin: 0 0 calc(var(--spacing-unit) * 1.5) 0;
    color: var(--text-primary);
}

.card-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: calc(var(--spacing-unit) * 2);
    line-height: 1.5;
}

.card-tags {
    margin-bottom: calc(var(--spacing-unit) * 2);
}

.card-tags span {
    display: inline-block;
    background-color: var(--bg-secondary);
    color: var(--text-muted);
    padding: calc(var(--spacing-unit) * 0.5) var(--spacing-unit);
    border-radius: calc(var(--border-radius) / 2);
    font-size: 0.8rem;
    margin-right: var(--spacing-unit);
    margin-bottom: calc(var(--spacing-unit) * 0.5);
}

.card-action {
    width: 100%;
    padding: calc(var(--spacing-unit) * 1.5);
    border: none;
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-size: 14px;
}

.card-action:hover {
    background-color: var(--secondary-color);
}

.no-results {
    text-align: center;
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-top: calc(var(--spacing-unit) * 4);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        grid-template-areas:
            "header"
            "main";
        grid-template-columns: 1fr;
        grid-template-rows: 60px 1fr;
    }
    
    .sidebar {
        display: none;
    }
    
    .search-bar {
        display: none;
    }
    
    .header {
        padding: 0 calc(var(--spacing-unit) * 2);
    }
    
    .main-content {
        padding: calc(var(--spacing-unit) * 2);
    }
    
    .tool-grid {
        grid-template-columns: 1fr;
        gap: calc(var(--spacing-unit) * 2);
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .tool-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1025px) {
    .tool-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
}
