<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 头部 -->
    <header class="bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <div class="flex items-center">
            <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              ToolHub 工具集
            </h1>
          </div>

          <!-- 搜索栏 -->
          <SearchBar
            :search-term="searchTerm"
            @update:search-term="setSearchTerm"
          />

          <!-- 用户菜单 -->
          <div class="flex items-center space-x-4">
            <ThemeToggle />
          </div>
        </div>
      </div>
    </header>

    <div class="flex">
      <!-- 侧边栏 -->
      <Sidebar
        :categories="categories"
        :selected-category="selectedCategory"
        @update:selected-category="setSelectedCategory"
      />

      <!-- 主内容区 -->
      <main class="flex-1 overflow-y-auto">
        <div class="max-w-7xl mx-auto">
          <!-- 工具网格 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 auto-rows-fr gap-6 p-6">
            <ToolCard
              v-for="tool in filteredTools"
              :key="tool.id"
              :tool="tool"
              class="fade-in"
            />
          </div>

          <!-- 无结果提示 -->
          <div v-if="filteredTools.length === 0" class="text-center py-16">
            <div class="max-w-md mx-auto">
              <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47.901-6.06 2.377M15 7.5a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                未找到任何工具
              </h3>
              <p class="text-gray-500 dark:text-gray-400">
                尝试调整搜索条件或选择其他分类
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { provide } from 'vue'
import { useTools } from './composables/useTools.js'
import { useTheme } from './composables/useTheme.js'
import ToolCard from './components/ToolCard.vue'
import SearchBar from './components/SearchBar.vue'
import Sidebar from './components/Sidebar.vue'
import ThemeToggle from './components/ThemeToggle.vue'

const {
  categories,
  searchTerm,
  selectedCategory,
  filteredTools,
  setSearchTerm,
  setSelectedCategory
} = useTools()

// 主题管理
const { currentTheme } = useTheme()

// 提供主题给子组件
provide('theme', currentTheme)
</script>
