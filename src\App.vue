<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 吉卜力主题装饰 -->
    <GhibliDecorations />

    <!-- 头部 -->
    <header class="bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <div class="flex items-center">
            <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              ToolHub 工具集 v2.0
            </h1>
          </div>

          <!-- 搜索栏 -->
          <SearchBar
            :search-term="searchTerm"
            @update:search-term="setSearchTerm"
          />

          <!-- 用户菜单 -->
          <div class="flex items-center space-x-4">
            <ThemeToggle />
          </div>
        </div>
      </div>
    </header>

    <div class="flex">
      <!-- 侧边栏 -->
      <Sidebar
        :categories="categories"
        :selected-category="selectedCategory"
        @update:selected-category="setSelectedCategory"
        @update:collapsed="handleSidebarCollapse"
      />

      <!-- 主内容区 -->
      <main
        :class="[
          'flex-1 overflow-y-auto transition-all duration-300 ease-in-out',
          sidebarCollapsed ? 'ml-0' : 'ml-0'
        ]"
      >
        <div class="max-w-7xl mx-auto p-6">
          <!-- 工具网格 - 响应式网格布局 -->
          <div
            :class="[
              'grid gap-6 transition-all duration-300',
              getGridColumns()
            ]"
          >
            <ToolCard
              v-for="tool in filteredTools"
              :key="tool.id"
              :tool="tool"
              class="animate-in fade-in slide-in-from-bottom-4 duration-300"
              :style="{ animationDelay: `${Math.random() * 0.2}s` }"
            />
          </div>

          <!-- 无结果提示 -->
          <div v-if="filteredTools.length === 0" class="text-center py-16">
            <div class="max-w-md mx-auto">
              <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47.901-6.06 2.377M15 7.5a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                未找到任何工具
              </h3>
              <p class="text-gray-500 dark:text-gray-400">
                尝试调整搜索条件或选择其他分类
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, provide, onMounted } from 'vue'
import { useTools } from './composables/useTools.js'
import ToolCard from './components/ToolCard.vue'
import SearchBar from './components/SearchBar.vue'
import Sidebar from './components/Sidebar.vue'
import ThemeToggle from './components/ThemeToggle.vue'
import GhibliDecorations from './components/GhibliDecorations.vue'

const {
  categories,
  searchTerm,
  selectedCategory,
  filteredTools,
  setSearchTerm,
  setSelectedCategory
} = useTools()

// 侧边栏折叠状态
const sidebarCollapsed = ref(false)

// 当前主题
const currentTheme = ref('light')

// 提供主题给子组件
provide('theme', currentTheme)

// 处理侧边栏折叠
const handleSidebarCollapse = (collapsed) => {
  sidebarCollapsed.value = collapsed
}

// 响应式网格列数
const getGridColumns = () => {
  // 基于侧边栏状态和屏幕尺寸动态调整网格
  if (sidebarCollapsed.value) {
    return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5'
  } else {
    return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
  }
}

// 检测主题变化
onMounted(() => {
  const updateTheme = () => {
    const theme = document.documentElement.getAttribute('data-theme')
    currentTheme.value = theme || 'light'
  }

  // 初始检测
  updateTheme()

  // 监听主题变化
  const observer = new MutationObserver(updateTheme)
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['data-theme', 'class']
  })

  return () => observer.disconnect()
})
</script>

<style scoped>
/* 工具卡片进入动画 */
.fade-in {
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式工具网格 */
.responsive-tool-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  auto-rows: 1fr; /* 确保所有行高度相等 */
}

/* 平滑过渡效果 */
.transition-grid {
  transition: grid-template-columns 0.3s ease-in-out;
}

/* 主题切换时的平滑过渡 */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 确保在不同屏幕尺寸下的响应式布局 */
@media (max-width: 640px) {
  .responsive-tool-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .responsive-tool-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) and (max-width: 1280px) {
  .responsive-tool-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1281px) {
  .responsive-tool-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>
