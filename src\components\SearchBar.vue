<template>
  <div class="flex-1 max-w-lg mx-8">
    <div class="relative">
      <input
        type="text"
        :value="searchTerm"
        @input="handleInput"
        placeholder="搜索工具..."
        class="w-full px-4 py-3 pl-12 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
      />
      <!-- 搜索图标 -->
      <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </div>
      <!-- 清除按钮 -->
      <button
        v-if="searchTerm"
        @click="clearSearch"
        class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"
      >
        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup>
const emit = defineEmits(['update:searchTerm'])

defineProps({
  searchTerm: {
    type: String,
    default: ''
  }
})

const handleInput = (event) => {
  emit('update:searchTerm', event.target.value)
}

const clearSearch = () => {
  emit('update:searchTerm', '')
}
</script>
