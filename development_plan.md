# 工具扩展与维护方案

## 背景

当前，每当需要添加一个新的工具时，都需要直接修改 `index.html` 的代码。这种方式不仅繁琐，而且容易在复制粘贴中引入错误。

为了简化流程并增强网站的可维护性和扩展性，我们采用一种更现代化、数据驱动的方案。

## 核心方案：数据驱动

我们将不再在 HTML 中硬编码工具卡片。取而代之，我们在 `js/script.js` 文件的顶部定义一个 JavaScript 数组，这个数组将作为所有工具的“注册中心”。

主页面的所有工具卡片、搜索、筛选功能都将基于这个数组动态生成。

## 具体实现

在 `D:\code\nodeWork\game-door\tools\js\script.js` 文件中，我们将维护一个名为 `tools` 的数组：

```javascript
const tools = [
  {
    name: 'AI 作家',
    description: '生成高质量的文章和营销文案。',
    category: 'writing',
    tags: ['内容', 'SEO'],
    url: 'path/to/your/ai-writer.html' // 指向工具的实际HTML文件
  },
  {
    name: '图像生成器',
    description: '根据文本提示创建令人惊叹的视觉效果。',
    category: 'image',
    tags: ['艺术', '设计'],
    url: 'path/to/your/image-gen.html'
  },
  // 当您需要添加新工具时，只需在此处添加一个新对象
  {
    name: '我的新工具',
    description: '这是一个很棒的新工具。',
    category: 'code',
    tags: ['原创', '实用'],
    url: 'path/to/my-new-tool.html'
  }
];
```

## 如何添加一个新工具？

流程非常简单：

1.  **创建工具页面**：像平常一样，创建一个独立的 HTML 文件（例如 `my-new-tool.html`），并将其放置在合适的路径下。

2.  **注册新工具**：打开 `js/script.js` 文件，在 `tools` 数组中添加一个新的 JavaScript 对象，填入新工具的 `name` (名称), `description` (描述), `category` (分类), `tags` (标签数组), 和 `url` (指向您刚刚创建的HTML文件路径)。

3.  **完成**：保存文件。网站主页将会自动出现您新添加的工具卡片，无需任何其他操作。

## 方案优势

- **添加简单**：新增工具从“修改HTML结构”简化为“在数组中添加一条数据”。
- **维护高效**：未来若要修改所有卡片的样式或结构，只需修改一处渲染模板即可，无需逐个修改。
- **功能强大**：基于统一的数据源，可以轻松实现并扩展搜索、分类、筛选等高级功能。

