import { ref, computed } from 'vue'
import { tools, categories } from '../data/tools.js'

const searchTerm = ref('')
const selectedCategory = ref('all')

export function useTools() {
    // 过滤后的工具列表
    const filteredTools = computed(() => {
        return tools.filter(tool => {
            const categoryMatch = selectedCategory.value === 'all' || tool.category === selectedCategory.value
            const searchMatch = searchTerm.value === '' || 
                tool.name.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                tool.description.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                tool.tags.some(tag => tag.toLowerCase().includes(searchTerm.value.toLowerCase()))
            
            return categoryMatch && searchMatch
        })
    })

    // 设置搜索词
    const setSearchTerm = (term) => {
        searchTerm.value = term
    }

    // 设置选中的分类
    const setSelectedCategory = (category) => {
        selectedCategory.value = category
    }

    // 清空搜索
    const clearSearch = () => {
        searchTerm.value = ''
    }

    return {
        tools,
        categories,
        searchTerm,
        selectedCategory,
        filteredTools,
        setSearchTerm,
        setSelectedCategory,
        clearSearch
    }
}
