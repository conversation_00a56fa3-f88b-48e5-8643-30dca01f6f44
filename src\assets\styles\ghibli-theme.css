/* 吉卜力主题特殊样式 */

/* 浮动动画 - 模仿云朵飘动 */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

/* 水彩绽放动画 */
@keyframes watercolor-bloom {
    0% { 
        transform: scale(0.8);
        opacity: 0.7;
        filter: blur(2px);
    }
    50% { 
        transform: scale(1.05);
        opacity: 0.9;
        filter: blur(1px);
    }
    100% { 
        transform: scale(1);
        opacity: 1;
        filter: blur(0px);
    }
}

/* 魔法尘埃效果 */
@keyframes magic-dust {
    0% { 
        transform: translateY(0) scale(1);
        opacity: 0;
    }
    50% { 
        transform: translateY(-20px) scale(1.2);
        opacity: 1;
    }
    100% { 
        transform: translateY(-40px) scale(0.8);
        opacity: 0;
    }
}

/* 吉卜力主题专用样式 */
[data-theme="ghibli"] {
    /* 背景纹理效果 */
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(123, 176, 105, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(135, 206, 235, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(212, 165, 116, 0.05) 0%, transparent 50%);
}

[data-theme="ghibli"] .container {
    background: var(--bg-primary);
    position: relative;
    overflow: hidden;
}

/* 头部样式 - 手绘感觉 */
[data-theme="ghibli"] .header {
    background: linear-gradient(135deg, var(--bg-card) 0%, var(--bg-secondary) 100%);
    border-bottom: 3px solid var(--primary-color);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    box-shadow: var(--shadow);
    position: relative;
}

[data-theme="ghibli"] .header::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 0;
    right: 0;
    height: 5px;
    background: var(--ghibli-gradient);
    border-radius: var(--border-radius);
}

/* Logo样式 - 魔法感 */
[data-theme="ghibli"] .logo h1 {
    background: var(--ghibli-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(123, 176, 105, 0.3);
    animation: var(--animation-float);
}

/* 工具卡片 - 赛璐珞框架效果 */
[data-theme="ghibli"] .tool-card {
    background: var(--bg-card);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

[data-theme="ghibli"] .tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--ghibli-gradient);
}

[data-theme="ghibli"] .tool-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 12px 32px rgba(123, 176, 105, 0.25);
    animation: watercolor-bloom 0.6s ease-out;
}

/* 按钮样式 - 有机形状 */
[data-theme="ghibli"] button {
    background: var(--ghibli-gradient);
    border: none;
    border-radius: calc(var(--border-radius) + 4px);
    color: white;
    font-weight: 600;
    padding: 12px 24px;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

[data-theme="ghibli"] button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transition: all 0.6s ease;
    transform: translate(-50%, -50%);
}

[data-theme="ghibli"] button:hover::before {
    width: 300px;
    height: 300px;
}

[data-theme="ghibli"] button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(123, 176, 105, 0.4);
}

/* 搜索框 - 草图本风格 */
[data-theme="ghibli"] input[type="text"] {
    background: var(--bg-card);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    padding: 12px 16px;
    color: var(--text-primary);
    transition: var(--transition-smooth);
}

[data-theme="ghibli"] input[type="text"]:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(123, 176, 105, 0.2);
    transform: scale(1.02);
}

/* 侧边栏 - 卷轴效果 */
[data-theme="ghibli"] .sidebar {
    background: linear-gradient(180deg, var(--bg-card) 0%, var(--bg-secondary) 100%);
    border-right: 3px solid var(--primary-color);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

[data-theme="ghibli"] .sidebar nav ul li a {
    color: var(--text-primary);
    transition: var(--transition-smooth);
    border-radius: calc(var(--border-radius) - 4px);
    position: relative;
}

[data-theme="ghibli"] .sidebar nav ul li a:hover,
[data-theme="ghibli"] .sidebar nav ul li a.active {
    background: var(--ghibli-gradient);
    color: white;
    transform: translateX(8px);
}

/* 主题切换器 - 魔法神器风格 */
[data-theme="ghibli"] .theme-switcher select {
    background: var(--ghibli-gradient);
    color: white;
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    padding: 10px 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-smooth);
}

[data-theme="ghibli"] .theme-switcher select:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(123, 176, 105, 0.3);
}

/* 装饰元素 - 漂浮的魔法尘埃 */
[data-theme="ghibli"] .container::after {
    content: '✨';
    position: fixed;
    top: 20%;
    right: 10%;
    font-size: 24px;
    animation: magic-dust 4s infinite;
    pointer-events: none;
    z-index: 1;
}

/* 无结果提示 - 温馨风格 */
[data-theme="ghibli"] .no-results {
    text-align: center;
    padding: 40px;
    color: var(--text-secondary);
    font-size: 18px;
    background: var(--bg-card);
    border: 2px dashed var(--primary-color);
    border-radius: var(--border-radius);
    margin: 20px;
}

[data-theme="ghibli"] .no-results::before {
    content: '🌿';
    display: block;
    font-size: 48px;
    margin-bottom: 16px;
    animation: var(--animation-float);
}

/* 滚动条样式 */
[data-theme="ghibli"] ::-webkit-scrollbar {
    width: 12px;
}

[data-theme="ghibli"] ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
}

[data-theme="ghibli"] ::-webkit-scrollbar-thumb {
    background: var(--ghibli-gradient);
    border-radius: var(--border-radius);
}

[data-theme="ghibli"] ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}
