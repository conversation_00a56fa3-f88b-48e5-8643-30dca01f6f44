import { ref, onMounted, watch } from 'vue'

const theme = ref('light')

export function useTheme() {
    // 切换主题
    const toggleTheme = () => {
        theme.value = theme.value === 'light' ? 'dark' : 'light'
    }

    // 设置主题
    const setTheme = (newTheme) => {
        theme.value = newTheme
    }

    // 应用主题到DOM
    const applyTheme = (themeValue) => {
        const root = document.documentElement

        // 移除所有主题类
        root.classList.remove('dark')
        root.removeAttribute('data-theme')

        // 应用新主题
        switch (themeValue) {
            case 'dark':
                root.classList.add('dark')
                break
            case 'ghibli':
                root.setAttribute('data-theme', 'ghibli')
                break
            default:
                // light theme - 默认状态，不需要额外类
                break
        }
    }

    // 从localStorage加载主题
    const loadTheme = () => {
        const savedTheme = localStorage.getItem('theme')
        if (savedTheme && ['light', 'dark', 'ghibli'].includes(savedTheme)) {
            theme.value = savedTheme
        } else {
            // 检测系统主题偏好
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
            theme.value = prefersDark ? 'dark' : 'light'
        }
        applyTheme(theme.value)
    }

    // 保存主题到localStorage
    const saveTheme = (themeValue) => {
        localStorage.setItem('theme', themeValue)
    }

    // 监听主题变化
    watch(theme, (newTheme) => {
        applyTheme(newTheme)
        saveTheme(newTheme)
    })

    // 组件挂载时加载主题
    onMounted(() => {
        loadTheme()
    })

    return {
        theme,
        currentTheme: theme, // 别名，保持向后兼容
        toggleTheme,
        setTheme
    }
}
