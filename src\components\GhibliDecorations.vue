<template>
  <div v-if="isGhibliTheme" class="ghibli-decorations">
    <!-- 漂浮的魔法元素 -->
    <div class="floating-elements">
      <div class="magic-dust" v-for="i in 5" :key="i" :style="getDustStyle(i)">
        {{ getMagicSymbol(i) }}
      </div>
    </div>
    
    <!-- 背景装饰图案 -->
    <div class="background-patterns">
      <div class="pattern pattern-1">🌿</div>
      <div class="pattern pattern-2">🍃</div>
      <div class="pattern pattern-3">✨</div>
      <div class="pattern pattern-4">🌸</div>
    </div>
    
    <!-- 角落装饰 -->
    <div class="corner-decorations">
      <div class="corner corner-top-left">🌱</div>
      <div class="corner corner-top-right">🦋</div>
      <div class="corner corner-bottom-left">🌺</div>
      <div class="corner corner-bottom-right">🍄</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useTheme } from '../composables/useTheme.js'

const { theme } = useTheme()

const isGhibliTheme = computed(() => theme.value === 'ghibli')

const getMagicSymbol = (index) => {
  const symbols = ['✨', '🌟', '💫', '⭐', '🌙']
  return symbols[index % symbols.length]
}

const getDustStyle = (index) => {
  const positions = [
    { top: '10%', left: '15%', animationDelay: '0s' },
    { top: '25%', right: '20%', animationDelay: '1s' },
    { top: '60%', left: '10%', animationDelay: '2s' },
    { top: '80%', right: '15%', animationDelay: '3s' },
    { top: '40%', left: '80%', animationDelay: '4s' }
  ]
  return positions[index % positions.length]
}
</script>

<style scoped>
.ghibli-decorations {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  overflow: hidden;
}

.floating-elements {
  position: relative;
  width: 100%;
  height: 100%;
}

.magic-dust {
  position: absolute;
  font-size: 20px;
  animation: float-and-sparkle 8s infinite ease-in-out;
  opacity: 0.7;
}

@keyframes float-and-sparkle {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.7;
  }
  25% { 
    transform: translateY(-20px) rotate(90deg) scale(1.2);
    opacity: 1;
  }
  50% { 
    transform: translateY(-10px) rotate(180deg) scale(0.8);
    opacity: 0.5;
  }
  75% { 
    transform: translateY(-30px) rotate(270deg) scale(1.1);
    opacity: 0.9;
  }
}

.background-patterns {
  position: absolute;
  width: 100%;
  height: 100%;
}

.pattern {
  position: absolute;
  font-size: 24px;
  opacity: 0.1;
  animation: gentle-sway 12s infinite ease-in-out;
}

.pattern-1 {
  top: 20%;
  left: 5%;
  animation-delay: 0s;
}

.pattern-2 {
  top: 50%;
  right: 10%;
  animation-delay: 3s;
}

.pattern-3 {
  bottom: 30%;
  left: 20%;
  animation-delay: 6s;
}

.pattern-4 {
  top: 70%;
  right: 25%;
  animation-delay: 9s;
}

@keyframes gentle-sway {
  0%, 100% { 
    transform: translateX(0px) rotate(0deg);
  }
  33% { 
    transform: translateX(10px) rotate(5deg);
  }
  66% { 
    transform: translateX(-5px) rotate(-3deg);
  }
}

.corner-decorations {
  position: absolute;
  width: 100%;
  height: 100%;
}

.corner {
  position: absolute;
  font-size: 32px;
  opacity: 0.3;
  animation: corner-pulse 6s infinite ease-in-out;
}

.corner-top-left {
  top: 20px;
  left: 20px;
  animation-delay: 0s;
}

.corner-top-right {
  top: 20px;
  right: 20px;
  animation-delay: 1.5s;
}

.corner-bottom-left {
  bottom: 20px;
  left: 20px;
  animation-delay: 3s;
}

.corner-bottom-right {
  bottom: 20px;
  right: 20px;
  animation-delay: 4.5s;
}

@keyframes corner-pulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 0.3;
  }
  50% { 
    transform: scale(1.2);
    opacity: 0.6;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .magic-dust {
    font-size: 16px;
  }
  
  .pattern {
    font-size: 20px;
  }
  
  .corner {
    font-size: 24px;
  }
}
</style>
