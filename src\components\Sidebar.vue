<template>
  <aside
    :class="[
      'bg-white dark:bg-gray-900 shadow-lg border-r border-gray-200 dark:border-gray-700 h-full transition-all duration-300 ease-in-out',
      isCollapsed ? 'w-16' : 'w-64'
    ]"
  >
    <div class="p-4">
      <!-- 折叠/展开按钮 -->
      <div class="flex items-center justify-between mb-6">
        <h2
          v-if="!isCollapsed"
          class="text-lg font-bold text-gray-900 dark:text-white px-2 transition-opacity duration-300"
        >
          工具分类
        </h2>
        <button
          @click="toggleCollapse"
          :class="[
            'p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 group',
            isCollapsed ? 'mx-auto' : 'ml-auto'
          ]"
          :title="isCollapsed ? '展开侧边栏' : '折叠侧边栏'"
        >
          <!-- 汉堡菜单/箭头图标 -->
          <svg
            :class="[
              'w-5 h-5 text-gray-600 dark:text-gray-400 transition-transform duration-300',
              isCollapsed ? 'rotate-180' : ''
            ]"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              v-if="!isCollapsed"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M11 19l-7-7 7-7m8 14l-7-7 7-7"
            />
            <path
              v-else
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"
            />
          </svg>
        </button>
      </div>

      <!-- 分类导航 -->
      <nav>
        <ul class="space-y-1">
          <li v-for="category in categories" :key="category.id">
            <button
              :class="[
                'flex items-center text-sm font-medium rounded-lg mx-2 my-1 transition-all duration-200 cursor-pointer text-gray-700 dark:text-gray-300 w-full text-left hover:bg-gray-100 dark:hover:bg-gray-800 relative group',
                selectedCategory === category.id ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : '',
                isCollapsed ? 'px-2 py-3 justify-center' : 'px-4 py-3'
              ]"
              :data-category="category.id"
              @click="handleCategoryClick(category.id)"
              :title="isCollapsed ? category.name : ''"
            >
              <span
                :class="[
                  'text-lg transition-all duration-200',
                  isCollapsed ? 'mr-0' : 'mr-3'
                ]"
              >
                {{ getCategoryIcon(category.id) }}
              </span>

              <!-- 文字标签 - 折叠时隐藏 -->
              <span
                v-if="!isCollapsed"
                class="font-medium transition-opacity duration-300"
              >
                {{ category.name }}
              </span>

              <!-- 活跃状态指示器 -->
              <span
                v-if="selectedCategory === category.id && !isCollapsed"
                class="ml-auto transition-opacity duration-300"
              >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
              </span>

              <!-- 折叠状态下的工具提示 -->
              <div
                v-if="isCollapsed"
                class="absolute left-full ml-2 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-50"
              >
                {{ category.name }}
                <!-- 箭头 -->
                <div class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-900 dark:bg-gray-700 rotate-45"></div>
              </div>
            </button>
          </li>
        </ul>
      </nav>
    </div>
  </aside>
</template>

<script setup>
import { ref } from 'vue'

const emit = defineEmits(['update:selectedCategory'])

defineProps({
  categories: {
    type: Array,
    required: true
  },
  selectedCategory: {
    type: String,
    required: true
  }
})

// 折叠状态
const isCollapsed = ref(false)

const handleCategoryClick = (categoryId) => {
  emit('update:selectedCategory', categoryId)
}

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

// 根据分类ID返回对应的图标
const getCategoryIcon = (categoryId) => {
  const iconMap = {
    all: '🏠',
    writing: '✍️',
    image: '🖼️',
    video: '🎬',
    code: '💻',
    life: '🏠',
    development: '⚙️',
    encoding: '🔐',
    converters: '🔄',
    text: '📝',
    pdf: '📄',
    audio: '🎵',
    network: '🌐',
    office: '📊'
  }
  return iconMap[categoryId] || '📁'
}
</script>
