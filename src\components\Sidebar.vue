<template>
  <aside
    :class="[
      'bg-white dark:bg-gray-900 shadow-lg border-r border-gray-200 dark:border-gray-700 h-full transition-all duration-300 ease-in-out relative',
      isCollapsed ? 'w-16' : 'w-64'
    ]"
  >
    <!-- 折叠/展开按钮 -->
    <div class="absolute -right-3 top-6 z-10">
      <button
        @click="toggleCollapse"
        class="w-6 h-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-full shadow-md hover:shadow-lg transition-all duration-200 flex items-center justify-center group hover:scale-110"
        :title="isCollapsed ? '展开侧边栏' : '折叠侧边栏'"
      >
        <svg
          :class="[
            'w-3 h-3 text-gray-600 dark:text-gray-400 transition-transform duration-300',
            isCollapsed ? 'rotate-180' : ''
          ]"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>
    </div>

    <div class="p-4">
      <!-- 侧边栏标题 -->
      <h2
        :class="[
          'text-lg font-bold text-gray-900 dark:text-white mb-6 px-2 transition-all duration-300',
          isCollapsed ? 'opacity-0 scale-0' : 'opacity-100 scale-100'
        ]"
      >
        工具分类
      </h2>

      <!-- 分类导航 -->
      <nav>
        <ul class="space-y-1">
          <li v-for="category in categories" :key="category.id">
            <button
              :class="[
                'flex items-center text-sm font-medium rounded-lg mx-2 my-1 transition-all duration-200 cursor-pointer text-gray-700 dark:text-gray-300 w-full text-left hover:bg-gray-100 dark:hover:bg-gray-800 relative group',
                isCollapsed ? 'px-2 py-3 justify-center' : 'px-4 py-3',
                selectedCategory === category.id ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : ''
              ]"
              :data-category="category.id"
              @click="handleCategoryClick(category.id)"
              :title="isCollapsed ? category.name : ''"
            >
              <span :class="['text-lg', isCollapsed ? '' : 'mr-3']">{{ getCategoryIcon(category.id) }}</span>

              <!-- 文字标签 - 折叠时隐藏 -->
              <span
                :class="[
                  'font-medium transition-all duration-300',
                  isCollapsed ? 'opacity-0 scale-0 w-0' : 'opacity-100 scale-100'
                ]"
              >
                {{ category.name }}
              </span>

              <!-- 选中指示器 -->
              <span
                v-if="selectedCategory === category.id && !isCollapsed"
                class="ml-auto transition-all duration-300"
              >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
              </span>

              <!-- 折叠状态下的悬浮提示 -->
              <div
                v-if="isCollapsed"
                class="absolute left-full ml-2 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-50 pointer-events-none"
              >
                {{ category.name }}
                <div class="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-900 dark:bg-gray-700 rotate-45"></div>
              </div>
            </button>
          </li>
        </ul>
      </nav>
    </div>
  </aside>
</template>

<script setup>
import { ref, watch } from 'vue'

const emit = defineEmits(['update:selectedCategory', 'update:collapsed'])

defineProps({
  categories: {
    type: Array,
    required: true
  },
  selectedCategory: {
    type: String,
    required: true
  }
})

// 折叠状态
const isCollapsed = ref(false)

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
  emit('update:collapsed', isCollapsed.value)
}

// 监听折叠状态变化，保存到localStorage
watch(isCollapsed, (newValue) => {
  localStorage.setItem('sidebar-collapsed', JSON.stringify(newValue))
}, { immediate: false })

// 从localStorage恢复折叠状态
const savedCollapsed = localStorage.getItem('sidebar-collapsed')
if (savedCollapsed !== null) {
  isCollapsed.value = JSON.parse(savedCollapsed)
  emit('update:collapsed', isCollapsed.value)
}

const handleCategoryClick = (categoryId) => {
  emit('update:selectedCategory', categoryId)
}

// 根据分类ID返回对应的图标
const getCategoryIcon = (categoryId) => {
  const iconMap = {
    all: '🏠',
    writing: '✍️',
    image: '🖼️',
    video: '🎬',
    code: '💻',
    life: '🏠',
    development: '⚙️',
    encoding: '🔐',
    converters: '🔄',
    text: '📝',
    pdf: '📄',
    audio: '🎵',
    network: '🌐',
    office: '📊'
  }
  return iconMap[categoryId] || '📁'
}
</script>
