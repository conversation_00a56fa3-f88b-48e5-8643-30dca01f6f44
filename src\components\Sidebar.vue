<template>
  <aside class="w-64 bg-white dark:bg-gray-900 shadow-lg border-r border-gray-200 dark:border-gray-700 h-full">
    <div class="p-4">
      <!-- 侧边栏标题 -->
      <h2 class="text-lg font-bold text-gray-900 dark:text-white mb-6 px-2">
        工具分类
      </h2>

      <!-- 分类导航 -->
      <nav>
        <ul class="space-y-1">
          <li v-for="category in categories" :key="category.id">
            <button
              :class="[
                'flex items-center px-4 py-3 text-sm font-medium rounded-lg mx-2 my-1 transition-all duration-200 cursor-pointer text-gray-700 dark:text-gray-300 w-full text-left hover:bg-gray-100 dark:hover:bg-gray-800',
                selectedCategory === category.id ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : ''
              ]"
              :data-category="category.id"
              @click="handleCategoryClick(category.id)"
            >
              <span class="mr-3 text-lg">{{ getCategoryIcon(category.id) }}</span>
              <span class="font-medium">{{ category.name }}</span>
              <span v-if="selectedCategory === category.id" class="ml-auto">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
              </span>
            </button>
          </li>
        </ul>
      </nav>
    </div>
  </aside>
</template>

<script setup>
const emit = defineEmits(['update:selectedCategory'])

defineProps({
  categories: {
    type: Array,
    required: true
  },
  selectedCategory: {
    type: String,
    required: true
  }
})

const handleCategoryClick = (categoryId) => {
  emit('update:selectedCategory', categoryId)
}

// 根据分类ID返回对应的图标
const getCategoryIcon = (categoryId) => {
  const iconMap = {
    all: '🏠',
    writing: '✍️',
    image: '🖼️',
    video: '🎬',
    code: '💻',
    life: '🏠',
    development: '⚙️',
    encoding: '🔐',
    converters: '🔄',
    text: '📝',
    pdf: '📄',
    audio: '🎵',
    network: '🌐',
    office: '📊'
  }
  return iconMap[categoryId] || '📁'
}
</script>
