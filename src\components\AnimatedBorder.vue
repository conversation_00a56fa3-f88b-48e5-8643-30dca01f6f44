<template>
  <div class="relative overflow-hidden rounded-xl">
    <!-- SVG动画边框 -->
    <svg
      class="absolute inset-0 w-full h-full pointer-events-none"
      :class="{ 'animate-border-glow': isHovered }"
    >
      <!-- 渐变定义 -->
      <defs>
        <!-- 主题渐变 -->
        <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" :stop-color="gradientColors.start" />
          <stop offset="50%" :stop-color="gradientColors.middle" />
          <stop offset="100%" :stop-color="gradientColors.end" />
        </linearGradient>
        
        <!-- 发光滤镜 -->
        <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
          <feMerge> 
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/> 
          </feMerge>
        </filter>
        
        <!-- 粒子轨迹滤镜 -->
        <filter id="particle-trail" x="-20%" y="-20%" width="140%" height="140%">
          <feGaussianBlur stdDeviation="2" result="blur"/>
          <feOffset dx="1" dy="1" result="offset"/>
          <feMerge>
            <feMergeNode in="offset"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>
      
      <!-- 动画边框路径 -->
      <rect
        x="1"
        y="1"
        :width="width - 2"
        :height="height - 2"
        rx="12"
        ry="12"
        fill="none"
        stroke="url(#borderGradient)"
        :stroke-width="strokeWidth"
        :stroke-dasharray="dashArray"
        :stroke-dashoffset="dashOffset"
        :filter="filterEffect"
        class="transition-all duration-300 ease-in-out"
      />
      
      <!-- 粒子轨迹点 -->
      <circle
        v-if="animationType === 'particle'"
        :cx="particlePosition.x"
        :cy="particlePosition.y"
        r="3"
        :fill="gradientColors.middle"
        :filter="filterEffect"
        class="animate-pulse"
      />
    </svg>
    
    <!-- 内容插槽 -->
    <div class="relative z-10">
      <slot />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
  animationType: {
    type: String,
    default: 'gradient', // 'gradient', 'glow', 'particle'
    validator: (value) => ['gradient', 'glow', 'particle'].includes(value)
  },
  theme: {
    type: String,
    default: 'light' // 'light', 'dark', 'ghibli'
  },
  width: {
    type: Number,
    default: 300
  },
  height: {
    type: Number,
    default: 200
  }
})

// 响应式状态
const isHovered = ref(false)
const dashOffset = ref(0)
const particlePosition = ref({ x: 0, y: 0 })
let animationFrame = null

// 计算属性
const gradientColors = computed(() => {
  const themes = {
    light: {
      start: '#3B82F6',
      middle: '#8B5CF6', 
      end: '#06B6D4'
    },
    dark: {
      start: '#60A5FA',
      middle: '#A78BFA',
      end: '#34D399'
    },
    ghibli: {
      start: '#7B9E89',
      middle: '#5B8DAB',
      end: '#D4B896'
    }
  }
  return themes[props.theme] || themes.light
})

const strokeWidth = computed(() => {
  return isHovered.value ? 3 : 2
})

const dashArray = computed(() => {
  if (props.animationType === 'gradient') {
    return isHovered.value ? '20 10' : '0'
  }
  return '0'
})

const filterEffect = computed(() => {
  switch (props.animationType) {
    case 'glow':
      return isHovered.value ? 'url(#glow)' : 'none'
    case 'particle':
      return 'url(#particle-trail)'
    default:
      return 'none'
  }
})

// 动画函数
const animateBorder = () => {
  if (props.animationType === 'gradient' && isHovered.value) {
    dashOffset.value -= 2
    if (dashOffset.value <= -30) {
      dashOffset.value = 0
    }
  }
  
  if (props.animationType === 'particle' && isHovered.value) {
    const time = Date.now() * 0.002
    const perimeter = 2 * (props.width + props.height - 4)
    const progress = (time % 4) / 4
    
    if (progress < 0.25) {
      // 顶边
      particlePosition.value.x = 1 + (props.width - 2) * (progress * 4)
      particlePosition.value.y = 1
    } else if (progress < 0.5) {
      // 右边
      particlePosition.value.x = props.width - 1
      particlePosition.value.y = 1 + (props.height - 2) * ((progress - 0.25) * 4)
    } else if (progress < 0.75) {
      // 底边
      particlePosition.value.x = props.width - 1 - (props.width - 2) * ((progress - 0.5) * 4)
      particlePosition.value.y = props.height - 1
    } else {
      // 左边
      particlePosition.value.x = 1
      particlePosition.value.y = props.height - 1 - (props.height - 2) * ((progress - 0.75) * 4)
    }
  }
  
  animationFrame = requestAnimationFrame(animateBorder)
}

// 事件处理
const handleMouseEnter = () => {
  isHovered.value = true
}

const handleMouseLeave = () => {
  isHovered.value = false
}

// 生命周期
onMounted(() => {
  animateBorder()
})

onUnmounted(() => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }
})

// 暴露事件给父组件
defineExpose({
  handleMouseEnter,
  handleMouseLeave
})
</script>

<style scoped>
@keyframes border-glow {
  0%, 100% {
    filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.8));
  }
}

.animate-border-glow {
  animation: border-glow 2s ease-in-out infinite;
}

/* 确保SVG在不同主题下的颜色正确 */
[data-theme="ghibli"] .animate-border-glow {
  animation: border-glow-ghibli 2s ease-in-out infinite;
}

@keyframes border-glow-ghibli {
  0%, 100% {
    filter: drop-shadow(0 0 5px rgba(123, 158, 137, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 20px rgba(123, 158, 137, 0.8));
  }
}
</style>
