<template>
  <div class="relative w-full h-full">
    <!-- SVG 动画边框 -->
    <svg
      class="absolute inset-0 w-full h-full pointer-events-none"
      :class="{ 'opacity-0': !isHovered, 'opacity-100': isHovered }"
      style="transition: opacity 0.3s ease-in-out;"
    >
      <defs>
        <!-- 渐变定义 -->
        <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" :stop-color="gradientColors.start" />
          <stop offset="50%" :stop-color="gradientColors.middle" />
          <stop offset="100%" :stop-color="gradientColors.end" />
        </linearGradient>

        <!-- 发光滤镜 -->
        <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
          <feMerge> 
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>

        <!-- 粒子轨迹滤镜 -->
        <filter id="particle-glow" x="-100%" y="-100%" width="300%" height="300%">
          <feGaussianBlur stdDeviation="2" result="blur"/>
          <feColorMatrix in="blur" mode="matrix" values="1 0 0 0 0  0 0 1 0 0  0 0 0 1 0  0 0 0 18 -7"/>
          <feBlend in="SourceGraphic" in2="blur" mode="normal"/>
        </filter>
      </defs>

      <!-- 主边框路径 -->
      <rect
        x="2"
        y="2"
        :width="width - 4"
        :height="height - 4"
        :rx="borderRadius"
        fill="none"
        stroke="url(#borderGradient)"
        :stroke-width="strokeWidth"
        :stroke-dasharray="dashArray"
        :stroke-dashoffset="dashOffset"
        filter="url(#glow)"
        class="animate-stroke-dash"
      />

      <!-- 粒子轨迹效果 -->
      <g v-if="effect === 'particles'">
        <circle
          v-for="(particle, index) in particles"
          :key="index"
          :cx="particle.x"
          :cy="particle.y"
          :r="particle.size"
          :fill="particle.color"
          :opacity="particle.opacity"
          filter="url(#particle-glow)"
          class="animate-particle-float"
          :style="{ animationDelay: `${particle.delay}s` }"
        />
      </g>

      <!-- 发光点效果 -->
      <g v-if="effect === 'glow'">
        <circle
          :cx="glowPoint.x"
          :cy="glowPoint.y"
          r="4"
          fill="#3b82f6"
          opacity="0.8"
          filter="url(#glow)"
          class="animate-glow"
        >
          <animateMotion
            :dur="`${animationDuration}s`"
            repeatCount="indefinite"
            rotate="auto"
          >
            <mpath href="#borderPath"/>
          </animateMotion>
        </circle>
      </g>

      <!-- 隐藏的路径用于动画 -->
      <path
        id="borderPath"
        :d="borderPath"
        fill="none"
        stroke="none"
      />
    </svg>

    <!-- 内容插槽 -->
    <div class="relative z-10 w-full h-full">
      <slot />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
  effect: {
    type: String,
    default: 'gradient', // 'gradient', 'glow', 'particles'
    validator: (value) => ['gradient', 'glow', 'particles'].includes(value)
  },
  isHovered: {
    type: Boolean,
    default: false
  },
  theme: {
    type: String,
    default: 'light' // 'light', 'dark', 'ghibli'
  },
  borderRadius: {
    type: Number,
    default: 12
  },
  strokeWidth: {
    type: Number,
    default: 2
  },
  animationDuration: {
    type: Number,
    default: 3
  }
})

// 响应式数据
const width = ref(300)
const height = ref(200)
const dashOffset = ref(0)
const particles = ref([])
const glowPoint = ref({ x: 0, y: 0 })

// 计算属性
const dashArray = computed(() => {
  if (props.effect === 'gradient') {
    return '10 5'
  }
  return '0'
})

const borderPath = computed(() => {
  const r = props.borderRadius
  const w = width.value - 4
  const h = height.value - 4
  return `M ${r + 2} 2 
          L ${w - r + 2} 2 
          Q ${w + 2} 2 ${w + 2} ${r + 2}
          L ${w + 2} ${h - r + 2}
          Q ${w + 2} ${h + 2} ${w - r + 2} ${h + 2}
          L ${r + 2} ${h + 2}
          Q 2 ${h + 2} 2 ${h - r + 2}
          L 2 ${r + 2}
          Q 2 2 ${r + 2} 2 Z`
})

const gradientColors = computed(() => {
  const themes = {
    light: {
      start: '#3b82f6',
      middle: '#8b5cf6',
      end: '#06b6d4'
    },
    dark: {
      start: '#60a5fa',
      middle: '#a78bfa',
      end: '#34d399'
    },
    ghibli: {
      start: '#7B9E89',
      middle: '#D06B66',
      end: '#5B8DAB'
    }
  }
  return themes[props.theme] || themes.light
})

// 生成粒子
const generateParticles = () => {
  particles.value = Array.from({ length: 8 }, (_, i) => ({
    x: Math.random() * width.value,
    y: Math.random() * height.value,
    size: Math.random() * 3 + 1,
    color: Object.values(gradientColors.value)[Math.floor(Math.random() * 3)],
    opacity: Math.random() * 0.8 + 0.2,
    delay: Math.random() * 2
  }))
}

// 动画循环
let animationFrame = null
const animate = () => {
  dashOffset.value += 0.5
  if (dashOffset.value > 100) {
    dashOffset.value = 0
  }
  
  // 更新发光点位置
  const time = Date.now() * 0.001
  const progress = (time % props.animationDuration) / props.animationDuration
  const angle = progress * Math.PI * 2
  const centerX = width.value / 2
  const centerY = height.value / 2
  const radiusX = (width.value - 20) / 2
  const radiusY = (height.value - 20) / 2
  
  glowPoint.value = {
    x: centerX + Math.cos(angle) * radiusX,
    y: centerY + Math.sin(angle) * radiusY
  }
  
  animationFrame = requestAnimationFrame(animate)
}

// 更新尺寸
const updateDimensions = (element) => {
  if (element) {
    width.value = element.offsetWidth
    height.value = element.offsetHeight
  }
}

// 生命周期
onMounted(() => {
  const element = document.querySelector('.relative')
  updateDimensions(element)
  generateParticles()
  animate()
  
  // 监听尺寸变化
  const resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      updateDimensions(entry.target)
    }
  })
  
  if (element) {
    resizeObserver.observe(element)
  }
  
  onUnmounted(() => {
    if (animationFrame) {
      cancelAnimationFrame(animationFrame)
    }
    resizeObserver.disconnect()
  })
})

// 监听主题变化重新生成粒子
watch(() => props.theme, () => {
  generateParticles()
})
</script>

<style scoped>
@keyframes stroke-dash {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: 100;
  }
}

.animate-stroke-dash {
  animation: stroke-dash 3s linear infinite;
}
</style>
