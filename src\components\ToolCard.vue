<template>
  <div
    class="relative group h-full"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    :data-category="tool.category"
  >
    <!-- SVG动画边框 -->
    <svg
      class="absolute inset-0 w-full h-full pointer-events-none z-0"
      :class="{ 'animate-border-glow': isHovered }"
    >
      <!-- 渐变定义 -->
      <defs>
        <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" :stop-color="gradientColors.start" />
          <stop offset="50%" :stop-color="gradientColors.middle" />
          <stop offset="100%" :stop-color="gradientColors.end" />
        </linearGradient>

        <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>

      <!-- 动画边框路径 -->
      <rect
        x="1"
        y="1"
        width="calc(100% - 2px)"
        height="calc(100% - 2px)"
        rx="12"
        ry="12"
        fill="none"
        stroke="url(#borderGradient)"
        :stroke-width="strokeWidth"
        :stroke-dasharray="dashArray"
        :stroke-dashoffset="dashOffset"
        :filter="filterEffect"
        class="transition-all duration-300 ease-in-out"
      />
    </svg>

    <!-- 卡片内容 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700 p-6 hover:transform hover:-translate-y-1 hover:scale-[1.02] flex flex-col h-full relative z-10">
      <!-- 工具图标 -->
      <div class="flex items-center justify-center w-14 h-14 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-xl mb-4 group-hover:scale-110 transition-transform duration-200 flex-shrink-0">
        <span class="text-2xl">{{ getToolIcon(tool.category) }}</span>
      </div>

      <!-- 工具信息 -->
      <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200 flex-shrink-0">
        {{ tool.name }}
      </h3>

      <!-- 描述文本 - 固定高度确保一致性 -->
      <div class="mb-4 flex-shrink-0">
        <p class="text-gray-600 dark:text-gray-400 text-sm leading-relaxed h-16 overflow-hidden">
          {{ truncatedDescription }}
        </p>
      </div>

      <!-- 标签 -->
      <div class="flex flex-wrap gap-2 mb-6 flex-grow">
        <span
          v-for="tag in tool.tags"
          :key="tag"
          class="px-3 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
        >
          {{ tag }}
        </span>
      </div>

      <!-- 操作按钮 - 始终在底部 -->
      <div class="mt-auto flex-shrink-0">
        <a :href="tool.url" class="block">
          <button class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg w-full group-hover:shadow-xl">
            <span class="flex items-center justify-center">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
              </svg>
              使用工具
            </span>
          </button>
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, inject } from 'vue'

const props = defineProps({
  tool: {
    type: Object,
    required: true
  }
})

// 注入主题
const currentTheme = inject('theme', 'light')

// 响应式状态
const isHovered = ref(false)
const dashOffset = ref(0)
let animationFrame = null

// 计算属性
const gradientColors = computed(() => {
  const themes = {
    light: {
      start: '#3B82F6',
      middle: '#8B5CF6',
      end: '#06B6D4'
    },
    dark: {
      start: '#60A5FA',
      middle: '#A78BFA',
      end: '#34D399'
    },
    ghibli: {
      start: '#7B9E89',
      middle: '#5B8DAB',
      end: '#D4B896'
    }
  }
  return themes[currentTheme.value] || themes.light
})

const strokeWidth = computed(() => {
  return isHovered.value ? 3 : 2
})

const dashArray = computed(() => {
  return isHovered.value ? '20 10' : '0'
})

const filterEffect = computed(() => {
  return isHovered.value ? 'url(#glow)' : 'none'
})

// 截断描述文本确保高度一致
const truncatedDescription = computed(() => {
  const maxLength = 80
  if (props.tool.description.length <= maxLength) {
    return props.tool.description
  }
  return props.tool.description.substring(0, maxLength) + '...'
})

// 动画函数
const animateBorder = () => {
  if (isHovered.value) {
    dashOffset.value -= 2
    if (dashOffset.value <= -30) {
      dashOffset.value = 0
    }
  }
  animationFrame = requestAnimationFrame(animateBorder)
}

// 事件处理
const handleMouseEnter = () => {
  isHovered.value = true
}

const handleMouseLeave = () => {
  isHovered.value = false
}

// 根据工具分类返回对应的图标
const getToolIcon = (category) => {
  const iconMap = {
    writing: '✍️',
    image: '🖼️',
    video: '🎬',
    code: '💻',
    life: '🏠',
    development: '⚙️',
    encoding: '🔐',
    converters: '🔄',
    text: '📝',
    pdf: '📄',
    audio: '🎵',
    network: '🌐',
    office: '📊'
  }
  return iconMap[category] || '🔧'
}

// 生命周期
onMounted(() => {
  animateBorder()
})

onUnmounted(() => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }
})
</script>

<style scoped>
@keyframes border-glow {
  0%, 100% {
    filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.8));
  }
}

.animate-border-glow {
  animation: border-glow 2s ease-in-out infinite;
}

/* 吉卜力主题下的发光效果 */
[data-theme="ghibli"] .animate-border-glow {
  animation: border-glow-ghibli 2s ease-in-out infinite;
}

@keyframes border-glow-ghibli {
  0%, 100% {
    filter: drop-shadow(0 0 5px rgba(123, 158, 137, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 20px rgba(123, 158, 137, 0.8));
  }
}

/* 确保描述文本的高度一致 */
.h-16 {
  height: 4rem;
}

/* 优化SVG渲染性能 */
svg {
  will-change: stroke-dashoffset, filter;
}
</style>
