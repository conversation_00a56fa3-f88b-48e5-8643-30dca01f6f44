<template>
  <div
    class="relative bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700 group hover:transform hover:-translate-y-1 hover:scale-[1.02] overflow-hidden"
    :data-category="tool.category"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
  >
    <!-- 动画边框 -->
    <AnimatedBorder
      :effect="borderEffect"
      :is-hovered="isHovered"
      :theme="currentTheme"
      :border-radius="12"
      :stroke-width="2"
      :animation-duration="3"
    />

    <!-- 卡片内容 - 固定高度确保统一 -->
    <div class="relative z-10 p-6 flex flex-col h-80">
      <!-- 工具图标 -->
      <div class="flex items-center justify-center w-14 h-14 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-xl mb-4 group-hover:scale-110 transition-transform duration-200">
        <span class="text-2xl">{{ getToolIcon(tool.category) }}</span>
      </div>

      <!-- 工具信息 -->
      <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
        {{ tool.name }}
      </h3>

      <!-- 描述文字 - 限制行数确保统一高度 -->
      <p class="text-gray-600 dark:text-gray-400 text-sm mb-4 leading-relaxed flex-grow overflow-hidden">
        <span class="line-clamp-3">{{ tool.description }}</span>
      </p>

      <!-- 标签 - 固定高度区域 -->
      <div class="flex flex-wrap gap-2 mb-6 min-h-[2.5rem] content-start">
        <span
          v-for="tag in tool.tags.slice(0, 3)"
          :key="tag"
          class="px-3 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
        >
          {{ tag }}
        </span>
        <span
          v-if="tool.tags.length > 3"
          class="px-3 py-1 text-xs font-medium bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400 rounded-full"
        >
          +{{ tool.tags.length - 3 }}
        </span>
      </div>

      <!-- 操作按钮 - 始终在底部 -->
      <div class="mt-auto">
        <a :href="tool.url" class="block">
          <button class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg w-full group-hover:shadow-xl relative overflow-hidden">
            <!-- 按钮发光效果 -->
            <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            <span class="relative flex items-center justify-center">
              <svg class="w-4 h-4 mr-2 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
              </svg>
              使用工具
            </span>
          </button>
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, inject } from 'vue'
import AnimatedBorder from './AnimatedBorder.vue'

// 悬停状态
const isHovered = ref(false)

// 获取当前主题
const currentTheme = inject('theme', ref('light'))

// 获取props
const props = defineProps({
  tool: {
    type: Object,
    required: true
  }
})

// 根据分类确定边框效果
const borderEffect = computed(() => {
  const effectMap = {
    writing: 'gradient',
    image: 'glow',
    video: 'particles',
    code: 'gradient',
    life: 'glow',
    development: 'particles',
    encoding: 'gradient',
    converters: 'glow',
    text: 'gradient',
    pdf: 'glow',
    audio: 'particles',
    network: 'gradient',
    office: 'glow'
  }
  return effectMap[props.tool.category] || 'gradient'
})

// 根据工具分类返回对应的图标
const getToolIcon = (category) => {
  const iconMap = {
    writing: '✍️',
    image: '🖼️',
    video: '🎬',
    code: '💻',
    life: '🏠',
    development: '⚙️',
    encoding: '🔐',
    converters: '🔄',
    text: '📝',
    pdf: '📄',
    audio: '🎵',
    network: '🌐',
    office: '📊'
  }
  return iconMap[category] || '🔧'
}
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 确保卡片在不同主题下的边框效果 */
.group:hover .animate-border {
  opacity: 1;
}

/* 按钮悬停效果增强 */
.group:hover button {
  transform: translateY(-1px);
}

/* 图标悬停旋转效果 */
.group:hover .text-2xl {
  animation: iconFloat 2s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-2px) rotate(5deg); }
}

/* 标签悬停效果 */
.group:hover .rounded-full {
  transform: scale(1.05);
}
</style>
