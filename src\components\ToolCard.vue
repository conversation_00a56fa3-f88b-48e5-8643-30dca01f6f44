<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700 p-6 group hover:transform hover:-translate-y-1 hover:scale-[1.02]" :data-category="tool.category">
    <!-- 工具图标 -->
    <div class="flex items-center justify-center w-14 h-14 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-xl mb-4 group-hover:scale-110 transition-transform duration-200">
      <span class="text-2xl">{{ getToolIcon(tool.category) }}</span>
    </div>

    <!-- 工具信息 -->
    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
      {{ tool.name }}
    </h3>
    <p class="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3 leading-relaxed">
      {{ tool.description }}
    </p>

    <!-- 标签 -->
    <div class="flex flex-wrap gap-2 mb-6">
      <span
        v-for="tag in tool.tags"
        :key="tag"
        class="px-3 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
      >
        {{ tag }}
      </span>
    </div>

    <!-- 操作按钮 -->
    <a :href="tool.url" class="block">
      <button class="btn-primary-enhanced group-hover:shadow-xl">
        <span class="flex items-center justify-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
          </svg>
          使用工具
        </span>
      </button>
    </a>
  </div>
</template>

<script setup>
defineProps({
  tool: {
    type: Object,
    required: true
  }
})

// 根据工具分类返回对应的图标
const getToolIcon = (category) => {
  const iconMap = {
    writing: '✍️',
    image: '🖼️',
    video: '🎬',
    code: '💻',
    life: '🏠',
    development: '⚙️',
    encoding: '🔐',
    converters: '🔄',
    text: '📝',
    pdf: '📄',
    audio: '🎵',
    network: '🌐',
    office: '📊'
  }
  return iconMap[category] || '🔧'
}
</script>
