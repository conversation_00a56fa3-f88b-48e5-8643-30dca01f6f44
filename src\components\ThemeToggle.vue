<template>
  <div class="relative">
    <select
      v-model="selectedTheme"
      @change="changeTheme"
      class="appearance-none bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 pr-8 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 cursor-pointer"
    >
      <option value="light">☀️ 明亮主题</option>
      <option value="dark">🌙 暗黑主题</option>
      <option value="ghibli">🌿 吉卜力主题</option>
    </select>
    <!-- 下拉箭头 -->
    <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useTheme } from '../composables/useTheme.js'

const { theme, setTheme } = useTheme()
const selectedTheme = ref(theme.value)

const changeTheme = () => {
  setTheme(selectedTheme.value)
}

watch(theme, (newTheme) => {
  selectedTheme.value = newTheme
})
</script>


