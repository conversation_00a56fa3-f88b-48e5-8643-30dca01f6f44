import { test, expect } from '@playwright/test';

test.describe('AI工具集 Vue 3应用', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('页面基本元素加载正确', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle('AI 工具集');
    
    // 检查主要元素是否存在
    await expect(page.getByRole('heading', { name: 'AI 工具集' })).toBeVisible();
    await expect(page.getByPlaceholder('搜索工具...')).toBeVisible();
    await expect(page.getByRole('button', { name: /切换主题/ })).toBeVisible();
    
    // 检查侧边栏导航
    await expect(page.getByRole('link', { name: '全部工具' })).toBeVisible();
    await expect(page.getByRole('link', { name: '写作' })).toBeVisible();
    await expect(page.getByRole('link', { name: '图像' })).toBeVisible();
    await expect(page.getByRole('link', { name: '视频' })).toBeVisible();
    await expect(page.getByRole('link', { name: '编程' })).toBeVisible();
  });

  test('工具卡片正确显示', async ({ page }) => {
    // 检查是否显示了工具卡片
    await expect(page.getByRole('heading', { name: 'AI 作家' })).toBeVisible();
    await expect(page.getByRole('heading', { name: '图像生成器' })).toBeVisible();
    await expect(page.getByRole('heading', { name: '代码助手' })).toBeVisible();
    
    // 检查工具卡片的基本结构
    const firstCard = page.locator('.tool-card').first();
    await expect(firstCard.locator('.card-title')).toBeVisible();
    await expect(firstCard.locator('.card-description')).toBeVisible();
    await expect(firstCard.locator('.card-tags')).toBeVisible();
    await expect(firstCard.locator('.card-action')).toBeVisible();
  });

  test('搜索功能正常工作', async ({ page }) => {
    const searchInput = page.getByPlaceholder('搜索工具...');
    
    // 搜索"代码"
    await searchInput.fill('代码');
    
    // 应该只显示包含"代码"的工具
    await expect(page.getByRole('heading', { name: '代码助手' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'AI 作家' })).not.toBeVisible();
    
    // 搜索"图像"
    await searchInput.fill('图像');
    await expect(page.getByRole('heading', { name: '图像生成器' })).toBeVisible();
    await expect(page.getByRole('heading', { name: '代码助手' })).not.toBeVisible();
    
    // 清空搜索
    await searchInput.fill('');
    await expect(page.getByRole('heading', { name: 'AI 作家' })).toBeVisible();
    await expect(page.getByRole('heading', { name: '图像生成器' })).toBeVisible();
  });

  test('分类筛选功能正常工作', async ({ page }) => {
    // 点击"写作"分类
    await page.getByRole('link', { name: '写作' }).click();
    
    // 应该只显示写作类工具
    await expect(page.getByRole('heading', { name: 'AI 作家' })).toBeVisible();
    await expect(page.getByRole('heading', { name: '语法检查器' })).toBeVisible();
    await expect(page.getByRole('heading', { name: '图像生成器' })).not.toBeVisible();
    
    // 检查活动状态
    await expect(page.getByRole('link', { name: '写作' })).toHaveClass(/active/);
    
    // 点击"图像"分类
    await page.getByRole('link', { name: '图像' }).click();
    await expect(page.getByRole('heading', { name: '图像生成器' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Logo 设计器' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'AI 作家' })).not.toBeVisible();
    
    // 点击"全部工具"
    await page.getByRole('link', { name: '全部工具' }).click();
    await expect(page.getByRole('heading', { name: 'AI 作家' })).toBeVisible();
    await expect(page.getByRole('heading', { name: '图像生成器' })).toBeVisible();
  });

  test('搜索和筛选组合功能', async ({ page }) => {
    // 先选择"编程"分类
    await page.getByRole('link', { name: '编程' }).click();
    
    // 然后搜索"API"
    await page.getByPlaceholder('搜索工具...').fill('API');
    
    // 应该只显示编程分类中包含"API"的工具
    await expect(page.getByRole('heading', { name: 'API 测试器' })).toBeVisible();
    await expect(page.getByRole('heading', { name: '代码助手' })).not.toBeVisible();
    
    // 搜索不存在的内容
    await page.getByPlaceholder('搜索工具...').fill('不存在的工具');
    await expect(page.getByText('未找到任何工具。')).toBeVisible();
  });

  test('主题切换功能正常工作', async ({ page }) => {
    // 检查初始主题（应该是light）
    const html = page.locator('html');
    await expect(html).toHaveAttribute('data-theme', 'light');
    
    // 点击主题切换按钮
    await page.getByRole('button', { name: /切换主题/ }).click();
    
    // 检查主题是否切换到dark
    await expect(html).toHaveAttribute('data-theme', 'dark');
    
    // 再次点击切换回light
    await page.getByRole('button', { name: /切换主题/ }).click();
    await expect(html).toHaveAttribute('data-theme', 'light');
  });

  test('主题持久化功能', async ({ page, context }) => {
    // 切换到暗色主题
    await page.getByRole('button', { name: /切换主题/ }).click();
    await expect(page.locator('html')).toHaveAttribute('data-theme', 'dark');
    
    // 刷新页面
    await page.reload();
    
    // 检查主题是否保持
    await expect(page.locator('html')).toHaveAttribute('data-theme', 'dark');
  });

  test('响应式布局 - 桌面端', async ({ page }) => {
    // 设置桌面端尺寸
    await page.setViewportSize({ width: 1200, height: 800 });
    
    // 检查侧边栏和搜索栏是否可见
    await expect(page.locator('.sidebar')).toBeVisible();
    await expect(page.getByPlaceholder('搜索工具...')).toBeVisible();
    
    // 检查工具网格布局
    const toolGrid = page.locator('.tool-grid');
    await expect(toolGrid).toBeVisible();
  });

  test('响应式布局 - 移动端', async ({ page }) => {
    // 设置移动端尺寸
    await page.setViewportSize({ width: 375, height: 667 });
    
    // 检查侧边栏和搜索栏是否隐藏
    await expect(page.locator('.sidebar')).not.toBeVisible();
    await expect(page.getByPlaceholder('搜索工具...')).not.toBeVisible();
    
    // 检查主要内容仍然可见
    await expect(page.getByRole('heading', { name: 'AI 工具集' })).toBeVisible();
    await expect(page.getByRole('button', { name: /切换主题/ })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'AI 作家' })).toBeVisible();
  });

  test('工具卡片交互', async ({ page }) => {
    // 检查工具卡片的悬停效果（通过检查CSS类）
    const firstCard = page.locator('.tool-card').first();
    await expect(firstCard).toBeVisible();
    
    // 检查使用工具按钮
    const useToolButton = firstCard.locator('.card-action');
    await expect(useToolButton).toBeVisible();
    await expect(useToolButton).toHaveText('使用工具');
  });

  test('页面性能 - 加载时间', async ({ page }) => {
    const startTime = Date.now();
    await page.goto('/');
    
    // 等待主要内容加载
    await expect(page.getByRole('heading', { name: 'AI 工具集' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'AI 作家' })).toBeVisible();
    
    const loadTime = Date.now() - startTime;
    
    // 页面应该在3秒内加载完成
    expect(loadTime).toBeLessThan(3000);
  });
});
